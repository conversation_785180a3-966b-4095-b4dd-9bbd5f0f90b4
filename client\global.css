/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Niramit:wght@200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Geist:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Merriweather:wght@300;400;700;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  /**
   * Tailwind CSS theme
   * tailwind.config.ts expects the following color variables to be expressed as HSL values.
   * A different format will require also updating the theme in tailwind.config.ts.
  */
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-niramit;
  }
}

/* NGO Custom Styles */
@layer components {
  .hero-gradient {
    background: linear-gradient(92deg, #EB414B 32.09%, #FDE047 97.65%);
  }

  .hero-bg {
    background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6));
  }

  .glass-card {
    background: rgba(255, 255, 255, 0.35);
    backdrop-filter: blur(14px);
    border-radius: 6px;
  }

  .section-gradient {
    background: linear-gradient(180deg, #FFF 0%, rgba(255, 255, 255, 0.10) 76.14%);
  }

  /* Consistent spacing utilities */
  .section-padding {
    @apply py-16 md:py-20 lg:py-24;
  }

  .container-padding {
    @apply px-4 md:px-6 lg:px-8;
  }

  .content-spacing {
    @apply space-y-6 md:space-y-8;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.6s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.6s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.4s ease-out;
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-all duration-300 hover:scale-105 hover:shadow-lg;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-xl hover:shadow-ngo-primary/20;
  }

  .hover-slide {
    @apply transition-all duration-300 hover:translate-x-1;
  }

  /* Button styles */
  .btn-primary {
    @apply bg-ngo-primary text-white px-6 py-3 rounded-md font-niramit font-medium transition-all duration-300 hover:bg-red-600 hover:scale-105 hover:shadow-lg;
  }

  .btn-secondary {
    @apply border border-ngo-secondary text-ngo-secondary px-6 py-3 rounded-md font-niramit font-medium transition-all duration-300 hover:bg-ngo-secondary hover:text-white hover:scale-105;
  }

  .btn-outline {
    @apply border border-white text-white px-6 py-3 rounded-md font-niramit font-medium transition-all duration-300 hover:bg-white hover:text-ngo-secondary hover:scale-105;
  }

  /* Card styles */
  .card-hover {
    @apply bg-white rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-lg hover:scale-105;
  }

  .card-interactive {
    @apply bg-white rounded-lg shadow-sm border border-gray-100 transition-all duration-300 hover:shadow-xl hover:scale-105 hover:border-ngo-primary/20;
  }
}

/* Keyframe animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Responsive typography */
@layer utilities {
  .text-responsive-xs {
    @apply text-sm md:text-base;
  }

  .text-responsive-sm {
    @apply text-base md:text-lg;
  }

  .text-responsive-md {
    @apply text-lg md:text-xl;
  }

  .text-responsive-lg {
    @apply text-xl md:text-2xl lg:text-3xl;
  }

  .text-responsive-xl {
    @apply text-2xl md:text-3xl lg:text-4xl;
  }

  .text-responsive-2xl {
    @apply text-3xl md:text-4xl lg:text-5xl;
  }
}
