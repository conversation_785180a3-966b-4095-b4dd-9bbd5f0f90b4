# Dependencies
node_modules/
/.pnp
.pnp.js

# Production builds
/dist/
/build/ 

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# Testing
/coverage
.nyc_output

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Vite cache
.vite/
.vite-cache/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~
.cursor/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# TypeScript
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Serverless directories
.serverless/

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Lock files (keep package-lock.json, ignore others)
yarn.lock
pnpm-lock.yaml

# Vercel
.vercel

# Netlify
.netlify/

